using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Users.Domain.Account;

namespace Users.Infrastructure.Data.Configurations.Account;

public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("Role", "Users");
        builder.HasData(new List<Role>(){
            new(){
                Id = Role.ADMIN,
                Name = "Admin",
                NormalizedName = "ADMIN",
                IsAssignable = false,
                IsBase = true,
                ConcurrencyStamp = Role.ADMIN.ToString()
            },
            new(){
                Id = Role.USER,
                Name = "User",
                NormalizedName = "USER",
                IsAssignable = false,
                IsBase = true,
                ConcurrencyStamp = Role.USER.ToString()
            },
            new(){
                Id = Role.AGENT,
                Name = "Call Center",
                NormalizedName = "AGENT",
                IsAssignable = true,
                IsBase = true,
                ConcurrencyStamp = Role.AGENT.ToString()
            },
            new(){
                Id = Role.LEADER,
                Name = "Departmen Görevlisi",
                NormalizedName = "LEADER",
                IsAssignable = true,
                IsBase = true,
                ConcurrencyStamp = Role.LEADER.ToString()
            }
        });
    }
}
